<?php
/**
 * Provincial Administration Manager - Districts Management View
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check user type and get appropriate districts
$current_user_id = get_current_user_id();
$user_type = get_user_meta($current_user_id, 'provincial_user_type', true);
$is_district_user = ($user_type === 'district');
$is_provincial_user = Provincial_User_Roles::user_has_provincial_access($current_user_id);

// Get districts based on user type
if ($is_district_user && !current_user_can('manage_options')) {
    // District users see only their assigned districts
    $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);

    if (!empty($assigned_districts)) {
        $districts = get_posts(array(
            'post_type' => 'esp_district',
            'numberposts' => -1,
            'post_status' => 'any',
            'post__in' => $assigned_districts,
            'orderby' => 'title',
            'order' => 'ASC'
        ));
    } else {
        $districts = array(); // No assigned districts
    }
} else {
    // Provincial users and administrators see all districts
    $districts = get_posts(array(
        'post_type' => 'esp_district',
        'numberposts' => -1,
        'post_status' => 'any',
        'orderby' => 'title',
        'order' => 'ASC'
    ));
}
?>

<div class="wrap">
    <div class="esp-admin-header">
        <h1><?php _e('Districts Management', 'esp-admin-manager'); ?></h1>
        <p><?php _e('Manage district information, statistics, and descriptions', 'esp-admin-manager'); ?></p>
    </div>

    <?php settings_errors('esp_messages'); ?>

    <!-- Quick Actions -->
    <div class="esp-quick-actions">
        <h3><?php _e('Quick Actions', 'esp-admin-manager'); ?></h3>
        <div class="actions">
            <a href="<?php echo admin_url('post-new.php?post_type=esp_district'); ?>" class="esp-button">
                <?php _e('Add New District', 'esp-admin-manager'); ?>
            </a>
            <a href="<?php echo admin_url('edit.php?post_type=esp_district'); ?>" class="esp-button secondary">
                <?php _e('Manage All Districts', 'esp-admin-manager'); ?>
            </a>
        </div>
    </div>

    <!-- Districts List -->
    <div class="esp-form-section">
        <h3><?php _e('Provincial Districts', 'esp-admin-manager'); ?></h3>
        
        <?php if (!empty($districts)): ?>
        <table class="esp-list-table">
            <thead>
                <tr>
                    <th><?php _e('District Name', 'esp-admin-manager'); ?></th>
                    <th><?php _e('LLGs', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Wards', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Population', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Area (km²)', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Status', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Actions', 'esp-admin-manager'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($districts as $district): 
                    $llgs = get_post_meta($district->ID, '_esp_district_llgs', true);
                    $wards = get_post_meta($district->ID, '_esp_district_wards', true);
                    $population = get_post_meta($district->ID, '_esp_district_population', true);
                    $area = get_post_meta($district->ID, '_esp_district_area', true);
                ?>
                <tr>
                    <td>
                        <strong><?php echo esc_html($district->post_title); ?></strong>
                        <div style="font-size: 12px; color: #666;">
                            <?php echo esc_html(wp_trim_words($district->post_content, 10)); ?>
                        </div>
                    </td>
                    <td>
                        <span style="font-size: 18px; font-weight: bold; color: var(--esp-primary);">
                            <?php echo esc_html($llgs ?: '—'); ?>
                        </span>
                    </td>
                    <td>
                        <span style="font-size: 18px; font-weight: bold; color: var(--esp-secondary);">
                            <?php echo esc_html($wards ?: '—'); ?>
                        </span>
                    </td>
                    <td>
                        <?php if ($population): ?>
                            <span style="font-size: 14px; color: #666;">
                                <?php echo esc_html($population); ?>
                            </span>
                        <?php else: ?>
                            <span style="color: #999;">—</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($area): ?>
                            <span style="font-size: 14px; color: #666;">
                                <?php echo esc_html($area); ?>
                            </span>
                        <?php else: ?>
                            <span style="color: #999;">—</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if ($district->post_status === 'publish'): ?>
                            <span style="color: green;">●</span> <?php _e('Published', 'esp-admin-manager'); ?>
                        <?php else: ?>
                            <span style="color: orange;">●</span> <?php echo esc_html(ucfirst($district->post_status)); ?>
                        <?php endif; ?>
                    </td>
                    <td class="actions">
                        <a href="<?php echo get_edit_post_link($district->ID); ?>" class="edit">
                            <?php _e('Edit', 'esp-admin-manager'); ?>
                        </a>
                        <a href="<?php echo get_delete_post_link($district->ID); ?>" class="delete" 
                           onclick="return confirm('<?php _e('Are you sure you want to delete this district?', 'esp-admin-manager'); ?>')">
                            <?php _e('Delete', 'esp-admin-manager'); ?>
                        </a>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php else: ?>
        <div class="esp-message warning">
            <p><?php _e('No districts found. Click "Add New District" to get started.', 'esp-admin-manager'); ?></p>
        </div>
        <?php endif; ?>
    </div>

    <!-- District Statistics Summary -->
    <div class="esp-form-section">
        <h3><?php _e('District Statistics Summary', 'esp-admin-manager'); ?></h3>
        <div class="esp-stats-grid">
            <div class="esp-stats-item">
                <label><?php _e('Total Districts', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: var(--esp-secondary);">
                    <?php echo count($districts); ?>
                </div>
            </div>
            
            <div class="esp-stats-item">
                <label><?php _e('Total LLGs', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: var(--esp-primary);">
                    <?php 
                    $total_llgs = 0;
                    foreach ($districts as $district) {
                        $llgs = get_post_meta($district->ID, '_esp_district_llgs', true);
                        $total_llgs += intval($llgs);
                    }
                    echo $total_llgs;
                    ?>
                </div>
            </div>
            
            <div class="esp-stats-item">
                <label><?php _e('Total Wards', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: var(--esp-accent);">
                    <?php 
                    $total_wards = 0;
                    foreach ($districts as $district) {
                        $wards = get_post_meta($district->ID, '_esp_district_wards', true);
                        $total_wards += intval($wards);
                    }
                    echo number_format($total_wards);
                    ?>
                </div>
            </div>
            
            <div class="esp-stats-item">
                <label><?php _e('Published', 'esp-admin-manager'); ?></label>
                <div style="font-size: 24px; font-weight: bold; color: green;">
                    <?php echo count(array_filter($districts, function($district) { return $district->post_status === 'publish'; })); ?>
                </div>
            </div>
        </div>
    </div>

    <!-- District Breakdown -->
    <?php if (!empty($districts)): ?>
    <div class="esp-form-section">
        <h3><?php _e('District Breakdown', 'esp-admin-manager'); ?></h3>
        <div class="esp-dashboard-cards">
            <?php foreach ($districts as $district): 
                $llgs = get_post_meta($district->ID, '_esp_district_llgs', true);
                $wards = get_post_meta($district->ID, '_esp_district_wards', true);
            ?>
            <div class="esp-dashboard-card">
                <h3><?php echo esc_html($district->post_title); ?></h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin: 10px 0;">
                    <div style="text-align: center;">
                        <div style="font-size: 20px; font-weight: bold; color: var(--esp-primary);">
                            <?php echo esc_html($llgs ?: '0'); ?>
                        </div>
                        <div style="font-size: 12px; color: #666;">LLGs</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 20px; font-weight: bold; color: var(--esp-secondary);">
                            <?php echo esc_html($wards ?: '0'); ?>
                        </div>
                        <div style="font-size: 12px; color: #666;">Wards</div>
                    </div>
                </div>
                <div class="description" style="font-size: 12px; margin-bottom: 15px;">
                    <?php echo esc_html(wp_trim_words($district->post_content, 15)); ?>
                </div>
                <a href="<?php echo get_edit_post_link($district->ID); ?>" class="button">
                    <?php _e('Edit District', 'esp-admin-manager'); ?>
                </a>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Preview Section -->
    <div class="esp-form-section">
        <h3><?php _e('Preview', 'esp-admin-manager'); ?></h3>
        <p><?php _e('This is how the districts will appear on your website:', 'esp-admin-manager'); ?></p>
        
        <div style="border: 1px solid #ddd; padding: 20px; background: #f9f9f9; border-radius: 8px; max-height: 400px; overflow-y: auto;">
            <?php echo do_shortcode('[dakoii_districts]'); ?>
        </div>
        
        <p style="margin-top: 15px;">
            <strong><?php _e('Shortcode:', 'esp-admin-manager'); ?></strong>
            <code>[dakoii_prov_admin_districts]</code> <?php _e('(Primary)', 'esp-admin-manager'); ?>
            <small style="color: #666; margin-left: 10px;"><?php _e('or', 'esp-admin-manager'); ?> <code>[dakoii_districts]</code>, <code>[esp_districts]</code></small>
        </p>
        <p>
            <?php _e('Options:', 'esp-admin-manager'); ?>
            <code>[dakoii_prov_admin_districts limit="3"]</code> - <?php _e('Show only 3 districts', 'esp-admin-manager'); ?><br>
            <code>[dakoii_prov_admin_districts show_stats="false"]</code> - <?php _e('Hide statistics', 'esp-admin-manager'); ?>
        </p>
    </div>

    <!-- Help Section -->
    <div class="esp-help">
        <h4><?php _e('Managing Districts', 'esp-admin-manager'); ?></h4>
        <p><?php _e('Districts are the primary administrative divisions of the Province. Here are some guidelines:', 'esp-admin-manager'); ?></p>
        <ul style="margin: 10px 0 0 20px;">
            <li><?php _e('Ensure all district names are spelled correctly and consistently', 'esp-admin-manager'); ?></li>
            <li><?php _e('Keep LLG and Ward counts accurate and up-to-date', 'esp-admin-manager'); ?></li>
            <li><?php _e('Write clear, informative descriptions for each district', 'esp-admin-manager'); ?></li>
            <li><?php _e('Include population and area data when available', 'esp-admin-manager'); ?></li>
            <li><?php _e('Use the description field to highlight unique features or characteristics', 'esp-admin-manager'); ?></li>
            <li><?php _e('Regular updates help citizens understand their local government structure', 'esp-admin-manager'); ?></li>
        </ul>
    </div>
</div>
